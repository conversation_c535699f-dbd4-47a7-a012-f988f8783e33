class CustomCoverageReporter {
  constructor(globalConfig, options) {
    this._globalConfig = globalConfig;
    this._options = options;
  }

  onRunComplete(contexts, results) {
    if (results.coverageMap && results.coverageMap.data) {
      console.log("-----------------|----------");
      console.log("File            | % Lines");
      console.log("-----------------|----------");
    }

    if (!results.coverageMap || !results.coverageMap.data) {
      return;
    }

    Object.entries(results.coverageMap.data).forEach(([file, fileCoverage]) => {
      const statements = fileCoverage?.s;

      if (!statements || Object.keys(statements).length === 0) {
        const fileName = file.split("\\").pop()?.replace(".ts", "") || file;
        const fileDisplay = fileName.padEnd(30).substring(0, 30);
        console.log(`${fileDisplay}| \x1b[33mN/A\x1b[0m`);
        return;
      }

      const totalStatements = Object.values(statements).length;
      const coveredStatements = Object.values(statements).filter((count) => count > 0).length;
      const lines = Math.round((coveredStatements / totalStatements) * 100);

      const fileName = file.split("\\").pop()?.replace(".ts", "") || file;
      const fileDisplay = fileName.padEnd(30).substring(0, 30);
      const coveragePercent = `\x1b[32m${lines.toString().padStart(3)}%\x1b[0m`; // Verde

      console.log(`${fileDisplay}| ${coveragePercent}`);
    });

    if (results.coverageMap && results.coverageMap.data) {
      console.log("-----------------|----------");
    }
  }
}

module.exports = CustomCoverageReporter;
