package de.interzero.oneepr.admin.report_set_price_list;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceListController;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceListRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.dto.CreateReportSetPriceListDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.dto.UpdateReportSetPriceListDto;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link ReportSetPriceListController}.
 * This class validates the full HTTP request-response cycle for the price list module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ReportSetPriceListControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReportSetPriceListRepository reportSetPriceListRepository;

    @Autowired
    private ReportSetRepository reportSetRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private CountryRepository countryRepository;

    private ReportSet testReportSet;

    private ReportSetPriceList testPriceList;

    /**
     * Sets up a consistent and valid database state before each test method runs.
     */
    @BeforeEach
    void setUp() {
        // Clear repositories in reverse order of dependency
        reportSetPriceListRepository.deleteAll();
        reportSetRepository.deleteAll();
        packagingServiceRepository.deleteAll();
        countryRepository.deleteAll();

        // Create prerequisite entities
        Country country = createAndSaveTestCountry();
        PackagingService packagingService = createAndSaveTestPackagingService(country);
        testReportSet = createAndSaveTestReportSet(packagingService);
        testPriceList = createAndSaveTestReportSetPriceList("Annual Fixed Price 2025", testReportSet);
    }

    /**
     * Verifies that a POST request successfully creates a new report set price list.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewPriceList() throws Exception {
        CreateReportSetPriceListDto createDto = new CreateReportSetPriceListDto();
        createDto.setReportSetId(testReportSet.getId());
        createDto.setTitle("Volume Based Price List");
        createDto.setLicenseYear(2026);
        createDto.setStartDate(Instant.parse("2026-01-01T00:00:00Z"));
        createDto.setEndDate(Instant.parse("2026-12-31T23:59:59Z"));
        createDto.setType(ReportSetPriceList.Type.PRICE_PER_VOLUME_BASE_PRICE);
        createDto.setBasePrice(150);

        mockMvc.perform(post(Api.REPORT_SET_PRICE_LISTS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.title", is("Volume Based Price List")))
                .andExpect(jsonPath("$.license_year", is(2026)))
                .andExpect(jsonPath("$.report_set_id", is(testReportSet.getId())));
    }

    /**
     * Verifies that a GET request returns all active report set price lists.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnListOfPriceLists() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_PRICE_LISTS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testPriceList.getId())));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct price list.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnCorrectPriceList() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_PRICE_LISTS + "/{id}", testPriceList.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testPriceList.getId())))
                .andExpect(jsonPath("$.title", is("Annual Fixed Price 2025")));
    }

    /**
     * Verifies that a PUT request successfully updates an existing price list.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void update_shouldModifyExistingPriceList() throws Exception {
        UpdateReportSetPriceListDto updateDto = new UpdateReportSetPriceListDto();
        updateDto.setTitle("Updated Price List Title");
        updateDto.setMinimumFee(500);

        mockMvc.perform(put(
                        Api.REPORT_SET_PRICE_LISTS + "/{id}",
                        testPriceList.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testPriceList.getId())))
                .andExpect(jsonPath("$.title", is("Updated Price List Title")))
                .andExpect(jsonPath("$.minimum_fee", is(500)));
    }

    /**
     * Verifies that a DELETE request correctly soft-deletes the target price list
     * AND all other price lists belonging to the same report set.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldSoftDeleteAllPriceListsInSameReportSet() throws Exception {
        // Setup: Create a second price list linked to the same report set
        ReportSetPriceList siblingPriceList = createAndSaveTestReportSetPriceList("Monthly Fee", testReportSet);

        // Action: Delete the first price list
        mockMvc.perform(delete(Api.REPORT_SET_PRICE_LISTS + "/{id}", testPriceList.getId())).andExpect(status().isOk());

        // Verification 1: The first price list should now be "not found" via the API
        mockMvc.perform(get(Api.REPORT_SET_PRICE_LISTS + "/{id}", testPriceList.getId()))
                .andExpect(status().isNotFound());

        // Verification 2: The sibling price list should ALSO be "not found" via the API
        mockMvc.perform(get(Api.REPORT_SET_PRICE_LISTS + "/{id}", siblingPriceList.getId()))
                .andExpect(status().isNotFound());

        // Verification 3: Both should have their deletedAt timestamp set in the database
        ReportSetPriceList deletedOriginal = reportSetPriceListRepository.findById(testPriceList.getId()).orElseThrow();
        ReportSetPriceList deletedSibling = reportSetPriceListRepository.findById(siblingPriceList.getId())
                .orElseThrow();

        assertNotNull(deletedOriginal.getDeletedAt());
        assertNotNull(deletedSibling.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        return countryRepository.saveAndFlush(country);
    }

    private PackagingService createAndSaveTestPackagingService(Country country) {
        PackagingService service = new PackagingService();
        service.setName("Test Service");
        service.setDescription("A test packaging service");
        service.setCountry(country);
        return packagingServiceRepository.saveAndFlush(service);
    }

    private ReportSet createAndSaveTestReportSet(PackagingService packagingService) {
        ReportSet reportSet = new ReportSet();
        reportSet.setName("Annual Test Report Set");
        reportSet.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        reportSet.setType(ReportSet.ReportSetType.FRACTIONS);
        reportSet.setPackagingService(packagingService);
        return reportSetRepository.saveAndFlush(reportSet);
    }

    private ReportSetPriceList createAndSaveTestReportSetPriceList(String title,
                                                                   ReportSet reportSet) {
        ReportSetPriceList priceList = new ReportSetPriceList();
        priceList.setReportSet(reportSet);
        priceList.setTitle(title);
        priceList.setLicenseYear(2025);
        priceList.setStartDate(Instant.now());
        priceList.setEndDate(Instant.now().plusSeconds(86400 * 365));
        priceList.setType(ReportSetPriceList.Type.FIXED_PRICE);
        priceList.setFixedPrice(10000);
        return reportSetPriceListRepository.saveAndFlush(priceList);
    }
}