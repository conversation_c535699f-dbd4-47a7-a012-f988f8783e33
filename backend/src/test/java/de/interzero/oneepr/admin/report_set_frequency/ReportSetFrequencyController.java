package de.interzero.oneepr.admin.report_set_frequency;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.ReportSetFrequency;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.ReportSetFrequencyController;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.ReportSetFrequencyRepository;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.dto.CreateReportSetFrequencyDto;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.dto.UpdateReportSetFrequencyDto;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Map;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link ReportSetFrequencyController}.
 * This class validates the full HTTP request-response cycle for the frequency module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ReportSetFrequencyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReportSetFrequencyRepository reportSetFrequencyRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private CountryRepository countryRepository;

    private PackagingService testPackagingService;

    private ReportSetFrequency testFrequency;

    /**
     * Sets up a consistent and valid database state before each test method runs.
     */
    @BeforeEach
    void setUp() {
        // Clear repositories in reverse order of dependency
        reportSetFrequencyRepository.deleteAll();
        packagingServiceRepository.deleteAll();
        countryRepository.deleteAll();

        // Create prerequisite entities
        Country country = createAndSaveTestCountry();
        testPackagingService = createAndSaveTestPackagingService(country);
        testFrequency = createAndSaveTestReportSetFrequency(testPackagingService);
    }

    /**
     * Verifies that a POST request successfully creates a new report set frequency.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewFrequency() throws Exception {
        CreateReportSetFrequencyDto createDto = new CreateReportSetFrequencyDto();
        createDto.setPackagingServiceId(testPackagingService.getId());
        createDto.setRhythm(ReportSetFrequency.Rhythm.QUARTERLY);
        Map<String, Object> frequencyJson = Map.of("deadline", Map.of("option", "LAST_DAY", "weekDay", "FRIDAY"));
        createDto.setFrequency(frequencyJson);

        mockMvc.perform(post(Api.REPORT_SET_FREQUENCIES).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.rhythm", is("QUARTERLY")))
                .andExpect(jsonPath("$.packaging_service_id", is(testPackagingService.getId())))
                .andExpect(jsonPath("$.frequency", is(objectMapper.writeValueAsString(frequencyJson))));
    }

    /**
     * Verifies that a GET request returns all active report set frequencies.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnListOfFrequencies() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_FREQUENCIES))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testFrequency.getId())));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct frequency.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnCorrectFrequency() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_FREQUENCIES + "/{id}", testFrequency.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testFrequency.getId())))
                .andExpect(jsonPath("$.rhythm", is("ANNUALLY")));
    }

    /**
     * Verifies that a PUT request successfully updates an existing frequency.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void update_shouldModifyExistingFrequency() throws Exception {
        UpdateReportSetFrequencyDto updateDto = new UpdateReportSetFrequencyDto();
        updateDto.setRhythm(ReportSetFrequency.Rhythm.MONTHLY);
        Map<String, Object> newFrequencyJson = Map.of("deadline", Map.of("day", 15));
        updateDto.setFrequency(newFrequencyJson);

        mockMvc.perform(put(
                        Api.REPORT_SET_FREQUENCIES + "/{id}",
                        testFrequency.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testFrequency.getId())))
                .andExpect(jsonPath("$.rhythm", is("MONTHLY")))
                .andExpect(jsonPath("$.frequency", is(objectMapper.writeValueAsString(newFrequencyJson))));
    }

    /**
     * Verifies that a DELETE request correctly soft-deletes a frequency.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldSoftDeleteFrequency() throws Exception {
        mockMvc.perform(delete(Api.REPORT_SET_FREQUENCIES + "/{id}", testFrequency.getId())).andExpect(status().isOk());

        // Verify it's gone from the "active" list via the API
        mockMvc.perform(get(Api.REPORT_SET_FREQUENCIES + "/{id}", testFrequency.getId()))
                .andExpect(status().isNotFound());

        // Verify in the database that the deleted_at field is now set
        ReportSetFrequency deletedFrequency = reportSetFrequencyRepository.findById(testFrequency.getId())
                .orElseThrow();
        assertNotNull(deletedFrequency.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        return countryRepository.saveAndFlush(country);
    }

    private PackagingService createAndSaveTestPackagingService(Country country) {
        PackagingService service = new PackagingService();
        service.setName("Test Service");
        service.setDescription("A test packaging service");
        service.setCountry(country);
        return packagingServiceRepository.saveAndFlush(service);
    }

    private ReportSetFrequency createAndSaveTestReportSetFrequency(PackagingService packagingService) {
        ReportSetFrequency frequency = new ReportSetFrequency();
        frequency.setPackagingService(packagingService);
        frequency.setRhythm(ReportSetFrequency.Rhythm.ANNUALLY);
        try {
            frequency.setFrequency(objectMapper.writeValueAsString(Collections.emptyMap()));
        } catch (Exception e) {
            // Should not happen in test
        }
        return reportSetFrequencyRepository.saveAndFlush(frequency);
    }
}