#database
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=***********************************************
spring.datasource.username=test_user
spring.datasource.password=test_password
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=validate
# Flyway configuration
spring.flyway.clean-disabled=false
spring.flyway.enabled=true
# Enable test gateway
mail.gateway=test