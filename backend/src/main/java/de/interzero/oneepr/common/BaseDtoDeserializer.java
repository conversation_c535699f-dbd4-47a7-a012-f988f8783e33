package de.interzero.oneepr.common;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

/**
 * Custom deserializer for {@link BaseDto} objects that tracks which fields were present in the JSON input.
 * This is useful for DTOs that extend BaseDto and need to know which fields were actually provided
 * during deserialization.
 */
public class BaseDtoDeserializer extends JsonDeserializer<BaseDto> implements ContextualDeserializer {

    private final Class<?> targetClass;

    public BaseDtoDeserializer(Class<?> targetClass) {
        this.targetClass = targetClass;
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt,
                                                BeanProperty property) {
        Class<?> contextualType = ctxt.getContextualType().getRawClass();
        return new BaseDtoDeserializer(contextualType);
    }

    @Override
    public BaseDto deserialize(JsonParser p,
                               DeserializationContext ctxt) throws IOException {
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        JsonNode node = mapper.readTree(p);

        try {
            // Create a temporary ObjectMapper with the same configuration as the parent
            ObjectMapper tempMapper = new ObjectMapper();

            // Copy essential configurations from the original mapper
            tempMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            tempMapper.registerModule(new JavaTimeModule()); // This was missing!
            tempMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

            // Copy the deserialization config from the original mapper
            tempMapper.setConfig(mapper.getDeserializationConfig());

            // Deserialize the object using the temporary mapper
            BaseDto dto = (BaseDto) tempMapper.readValue(node.toString(), targetClass);

            // Track which fields were present in the JSON
            Set<String> presentFields = new HashSet<>();
            Iterator<String> fieldNames = node.fieldNames();
            while (fieldNames.hasNext()) {
                presentFields.add(fieldNames.next());
            }

            dto.setPresentFields(presentFields);

            return dto;

        } catch (Exception e) {
            throw new IOException("Failed to deserialize " + targetClass.getSimpleName(), e);
        }
    }
}
