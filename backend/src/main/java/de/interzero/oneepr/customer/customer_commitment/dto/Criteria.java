package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Describes a detailed criterion, which can be used for "COMMITMENT" or "CALCULATOR" purposes.
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
public class Criteria {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("mode")
    private Mode mode;

    @JsonProperty("type")
    private Type type;

    @JsonProperty("title")
    private String title;

    @JsonProperty("help_text")
    private String helpText;

    @JsonProperty("input_type")
    private InputType inputType;

    @JsonProperty("calculator_type")
    private String calculatorType;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @JsonProperty("required_information_id")
    private Integer requiredInformationId;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("deleted_at")
    private String deletedAt;

    @JsonProperty("options")
    private List<Option> options;


    public enum Type {
        PACKAGING_SERVICE,
        REPORT_SET,
        REPORT_FREQUENCY,
        AUTHORIZE_REPRESENTATIVE,
        REPRESENTATIVE_TIER,
        OTHER_COST,
        PRICE_LIST,
        REQUIRED_INFORMATION
    }

    public enum Mode {
        COMMITMENT,
        CALCULATOR
    }

    /**
     * @ts-legacy input_type: "YES_NO";
     */
    public enum InputType {
        YES,
        NO
    }

    @Data
    public static class Option {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("option_value")
        private String optionValue;

        @JsonProperty("option_to_value")
        private String optionToValue;

        @JsonProperty("value")
        private String value;
    }
}