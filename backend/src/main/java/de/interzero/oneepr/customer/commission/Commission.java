package de.interzero.oneepr.customer.commission;

import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.customer.Customer;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "commission",
        schema = "public"
)
public class Commission {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "commission_id_gen"
    )
    @SequenceGenerator(
            name = "commission_id_gen",
            sequenceName = "commission_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "user_id",
            nullable = false
    )
    private Integer userId;

    @NotNull
    @Column(
            name = "commission_percentage",
            nullable = false
    )
    private Integer commissionPercentage;

    @NotNull
    @Column(
            name = "commission_value",
            nullable = false
    )
    private Integer commissionValue;

    @NotNull
    @Column(
            name = "net_turnover",
            nullable = false
    )
    private Integer netTurnover;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "coupon_id")
    private Coupon coupon;

    @Column(
            name = "coupon_code",
            length = Integer.MAX_VALUE
    )
    private String couponCode;

    @Column(name = "order_id")
    private Integer orderId;

    @Column(
            name = "affiliate_link",
            length = Integer.MAX_VALUE
    )
    private String affiliateLink;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "order_customer_id")
    private Customer orderCustomer;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "user_type",
            columnDefinition = "enum_commission_user_type not null"
    )
    @NotNull
    private UserType userType;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            columnDefinition = "enum_commission_type not null"
    )
    @NotNull
    private Type type;


    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "service_type")
    private ServiceType serviceType;

    /**
     * add used field
     */
    @NotNull
    @Column(
            name = "used",
            nullable = false
    )
    private Boolean used = false;

    public enum ServiceType {
        EU_LICENSE,
        DIRECT_LICENSE,
        ACTION_GUIDE
    }

    public enum UserType {
        CUSTOMER,
        PARTNER
    }

    public enum Type {
        AFFILIATE_LINK,
        COUPON
    }
}