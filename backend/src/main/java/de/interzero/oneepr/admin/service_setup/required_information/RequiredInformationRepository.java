package de.interzero.oneepr.admin.service_setup.required_information;

import de.interzero.oneepr.admin.country.Country;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RequiredInformationRepository extends JpaRepository<RequiredInformation, Integer> {

    List<RequiredInformation> findAllByDeletedAtIsNull();

    Optional<RequiredInformation> findByIdAndDeletedAtIsNull(Integer id);

    List<RequiredInformation> findByCountryAndDeletedAtIsNull(Country country);
    /**
     * Finds all active (non-deleted) required information items for a given country code,
     * eagerly fetching the associated file.
     * <p>
     * This query uses a {@code LEFT JOIN FETCH} to ensure that the optional {@code file}
     * relation is loaded in the same database query. This is a key performance optimization
     * that prevents potential N+1 issues when the file is accessed from the resulting
     * {@link RequiredInformation} objects.
     *
     * @param countryCode The unique code of the country for which to retrieve required information.
     * @return A list of {@link RequiredInformation} entities with their associated {@code file}
     *         relation eagerly fetched.

     */
    @Query("SELECT ri FROM RequiredInformation ri LEFT JOIN FETCH ri.files f JOIN ri.country c WHERE c.code = :countryCode AND ri.deletedAt IS NULL")
    List<RequiredInformation> findAllByCountryCodeAndDeletedAtIsNullWithFile(@Param("countryCode") String countryCode);
}
