package de.interzero.oneepr.admin.service_setup.required_information;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "required_information",
        schema = "public"
)
public class RequiredInformation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @Column(
            name = "question",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("question")
    private String question;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            nullable = false
    )
    @JsonProperty("type")
    private Type type;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "kind",
            nullable = false
    )
    @JsonProperty("kind")
    private Kind kind = Kind.COUNTRY_INFORMATION;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "country_id")
    @JsonIgnore
    @JsonProperty("country")
    private Country country;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(
            name = "file_id",
            unique = true
    )
    @JsonIgnore
    @JsonProperty("file")
    private Files files;

    @OneToMany(mappedBy = "requiredInformation")
    @JsonIgnore
    @JsonProperty("criterias")
    private List<Criteria> criterias = new ArrayList<>();

    @Transient
    @JsonProperty("country_id")
    public Integer getCountryId() {
        return (this.country != null) ? this.country.getId() : null;
    }

    @Transient
    @JsonProperty("file_id")
    public String getFileId() {
        return (this.files != null) ? this.files.getId() : null;
    }

    @Transient
    @JsonProperty("packaging_service_ids")
    public List<Integer> getPackagingServiceIds() {
        return this.packagingServices.stream().map(PackagingService::getId).toList();
    }

    @ManyToMany(
            fetch = FetchType.LAZY,
            cascade = {CascadeType.PERSIST, CascadeType.MERGE}
    )
    @JoinTable(
            name = "required_information_packaging_service",
            joinColumns = @JoinColumn(name = "required_information_id"),
            inverseJoinColumns = @JoinColumn(name = "packaging_service_id")
    )
    @JsonIgnore
    @JsonProperty("packaging_services")
    private List<PackagingService> packagingServices = new ArrayList<>();

    public enum Type {
        TEXT,
        NUMBER,
        DOCUMENT,
        FILE,
        IMAGE
    }

    public enum Kind {
        COUNTRY_INFORMATION,
        GENERAL_INFORMATION
    }

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    /**
     * Helper method to add a packaging service to this required information.
     * Maintains bidirectional relationship consistency.
     *
     * @param packagingService The packaging service to add
     */
    public void addPackagingService(PackagingService packagingService) {
        if (!this.packagingServices.contains(packagingService)) {
            this.packagingServices.add(packagingService);
            packagingService.getRequiredInformations().add(this);
        }
    }

    /**
     * Helper method to remove a packaging service from this required information.
     * Maintains bidirectional relationship consistency.
     *
     * @param packagingService The packaging service to remove
     */
    public void removePackagingService(PackagingService packagingService) {
        if (this.packagingServices.contains(packagingService)) {
            this.packagingServices.remove(packagingService);
            packagingService.getRequiredInformations().remove(this);
        }
    }
}