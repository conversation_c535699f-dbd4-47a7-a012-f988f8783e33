package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * This DTO is used as the request body for creating a new ReportSetColumn.
 * It contains all necessary fields to define a column, its position in a hierarchy,
 * and its relationships to fractions. It is typically used within a list when creating or updating a ReportSet.
 */
@Getter
@Setter
public class ReportSetColumnCreateDto extends BaseDto {

    @Schema(
            description = "The unique code for this column, used for linking.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "OWN_BRAND"
    )
    @JsonProperty("code")
    @NotBlank
    private String code;

    @Schema(
            description = "The display name of the column.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "Own Brand"
    )
    @JsonProperty("name")
    @NotBlank
    private String name;

    @Schema(
            description = "A detailed description of the column's purpose.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "Products sold under your own brand name."
    )
    @JsonProperty("description")
    @NotBlank
    private String description;

    @Schema(
            description = "The unit of measurement for data in this column.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = ReportSetColumn.UnitType.class,
            example = "KG"
    )
    @JsonProperty("unit_type")
    @NotNull
    private ReportSetColumn.UnitType unitType;

    @Schema(
            description = "The nesting level of the column in the hierarchy (1 for root).",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "1"
    )
    @JsonProperty("level")
    @NotNull
    private Integer level;

    @Schema(
            description = "The display order of the column among its siblings.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "1"
    )
    @JsonProperty("order")
    @NotNull
    private Integer order;

    @Schema(
            description = "The ID of the parent column. Omit or set to null for a root-level column.",
            example = "10"
    )
    @JsonProperty("parent_id")
    private Integer parentId;

    @Schema(
            description = "The code of the parent column. Omit or set to null for a root-level column.",
            example = "SALES_CHANNEL"
    )
    @JsonProperty("parent_code")
    private String parentCode;

    @Schema(description = "A list of fractions that this column applies to.")
    @JsonProperty("fractions")
    private List<ReportSetColumnFractionDetailDto> fractions;
}