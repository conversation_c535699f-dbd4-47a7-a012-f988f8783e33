package de.interzero.oneepr.admin.service_setup;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country_price_list.CountryPriceList;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTier;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section.ObligationCheckSection;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformation;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.third_party_cost.ThirdPartyCost;
import jakarta.persistence.Embeddable;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * An embeddable class that aggregates all service setup configurations for a Country.
 * This object does not have its own table in the database; instead, its relationships
 * are mapped as if they belonged directly to the owning Country entity.
 */
@Getter
@Setter
@Embeddable
public class ServiceSetup {

    /**
     * A list of packaging services offered for the country.
     * Note: The `mappedBy = "country"` refers to the 'country' field in the PackagingService entity.
     */
    @OneToMany(
            mappedBy = "country",
            fetch = FetchType.EAGER
    )
    @JsonProperty("packaging_services")
    private List<PackagingService> packagingServices = new ArrayList<>();

    /**
     * A list of obligation check sections.
     */
    @OneToMany(mappedBy = "country")
    @JsonIgnore // Keeping JsonIgnore as it was in the original Country entity
    @JsonProperty("obligation_check_sections")
    private List<ObligationCheckSection> obligationCheckSections = new ArrayList<>();

    /**
     * A list of criteria for obligation checks.
     */
    @OneToMany(
            mappedBy = "country",
            fetch = FetchType.EAGER
    )
    @JsonProperty("criterias")
    private List<Criteria> criterias = new ArrayList<>();

    /**
     * A list of required information for the service setup.
     */
    @OneToMany(mappedBy = "country")
    @JsonIgnore // Keeping JsonIgnore as it was in the original Country entity
    @JsonProperty("required_informations")
    private List<RequiredInformation> requiredInformations = new ArrayList<>();

    /**
     * A list of third-party costs (or "other costs").
     */
    @OneToMany(
            mappedBy = "country",
            fetch = FetchType.EAGER
    )
    @JsonProperty("other_costs")
    private List<ThirdPartyCost> thirdPartyCosts = new ArrayList<>();

    /**
     * A list of representative tiers available for the country.
     */
    @OneToMany(mappedBy = "country")
    @JsonProperty("representative_tiers")
    private List<RepresentativeTier> representativeTiers = new ArrayList<>();

    /**
     * A list of price lists associated with the country.
     */
    @OneToMany(mappedBy = "country")
    @JsonIgnore // Keeping JsonIgnore as it was in the original Country entity
    @JsonProperty("country_price_lists")
    private List<CountryPriceList> countryPriceLists = new ArrayList<>();

}