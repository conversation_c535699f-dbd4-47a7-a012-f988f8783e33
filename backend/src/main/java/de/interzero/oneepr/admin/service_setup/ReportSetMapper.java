package de.interzero.oneepr.admin.service_setup;

import de.interzero.oneepr.admin.service_setup.dto.ReportSetColumnDto;
import de.interzero.oneepr.admin.service_setup.dto.ReportSetFractionDto;
import de.interzero.oneepr.admin.service_setup.dto.ReportSetSetupDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
@SuppressWarnings("unused")
public interface ReportSetMapper {

    /**
     * Maps a ReportSetColumn entity to its DTO representation.
     * This method will now be used automatically when mapping ReportSet to ReportSetSetupDto.
     *
     * @param entity The ReportSetColumn entity.
     * @return The corresponding ReportSetColumnDto.
     */
    ReportSetColumnDto toDto(ReportSetColumn entity);

    /**
     * Maps a ReportSetFraction entity to its DTO representation.
     * This method will now be used automatically when mapping ReportSet to ReportSetSetupDto.
     *
     * @param entity The ReportSetFraction entity.
     * @return The corresponding ReportSetFractionDto.
     */
    @Mapping(
            source = "isActive",
            target = "active"
    )
    ReportSetFractionDto toDto(ReportSetFraction entity);

    ReportSetSetupDto toReportSetSetupDto(ReportSet entity,
                                          boolean hasCriteria);
}