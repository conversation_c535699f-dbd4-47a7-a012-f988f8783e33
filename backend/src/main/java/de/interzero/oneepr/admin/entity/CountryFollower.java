package de.interzero.oneepr.admin.entity;

import de.interzero.oneepr.admin.country.Country;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "country_follower",
        schema = "public"
)
public class CountryFollower {

    @Id
    @ColumnDefault("nextval('country_follower_id_seq')")
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "country_id",
            nullable = false
    )
    private Country country;

    @NotNull
    @Column(
            name = "user_id",
            nullable = false
    )
    private Integer userId;

    @NotNull
    @Column(
            name = "user_email",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String userEmail;

    @NotNull
    @Column(
            name = "user_first_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String userFirstName;

    @NotNull
    @Column(
            name = "user_last_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String userLastName;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private Instant deletedAt;

}