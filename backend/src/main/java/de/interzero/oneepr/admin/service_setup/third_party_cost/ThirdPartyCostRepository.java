package de.interzero.oneepr.admin.service_setup.third_party_cost;

import de.interzero.oneepr.admin.country.Country;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link ThirdPartyCost} entities.
 */
@Repository
public interface ThirdPartyCostRepository extends JpaRepository<ThirdPartyCost, Integer> {

    /**
     * Finds all non-deleted other costs.
     *
     * @return A list of other costs.
     */
    List<ThirdPartyCost> findAllByDeletedAtIsNull();

    /**
     * Finds a single non-deleted other cost by its ID.
     *
     * @param id The ID of the cost.
     * @return An optional containing the found cost.
     */
    @NonNull
    Optional<ThirdPartyCost> findByIdAndDeletedAtIsNull(@NonNull Integer id);

    List<ThirdPartyCost> findByCountryAndDeletedAtIsNull(Country country);

    /**
     * Finds all active (non-deleted) other costs for a given country code.
     * <p>
     * This query uses a {@code JOIN FETCH} to eagerly load the associated {@code Country}
     * entity in the same query, preventing potential N+1 performance issues when
     * the country information is accessed from the resulting {@code OtherCost} objects.
     *
     * @param countryCode The unique code of the country for which to retrieve other costs.
     * @return A list of {@link ThirdPartyCost} entities with their associated {@code Country}
     * eagerly fetched.
     */
    @EntityGraph(attributePaths = "country")
    List<ThirdPartyCost> findByCountry_CodeAndDeletedAtIsNull(String countryCode);
}