package de.interzero.oneepr.admin.mail;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Test implementation of {@link EmailOutboxGateway}, used for testing purposes only.
 *
 * <p>
 * This implementation does not send real emails. Instead, it stores all sent {@link EmailMessage}
 * instances in an internal list ({@link #sentMessages}) for inspection in tests and logs each
 * message via SLF4J.
 *
 * <p>
 * Activated when the property {@code mail.gateway=test} is set (e.g., in {@code application.properties}).
 */
@Getter
@Component
@ConditionalOnProperty(
        prefix = "mail",
        name = "gateway",
        havingValue = "test"
)
@Slf4j
public class TestEmailGateway implements EmailOutboxGateway {

    /**
     * Internal list of all {@link EmailMessage} objects that were "sent".
     * Useful for assertions in tests.
     */
    private final List<EmailMessage> sentMessages = new ArrayList<>();

    /**
     * Pretends to send an email message by storing it in {@link #sentMessages} and logging its content.
     *
     * <p>
     * This method is used to simulate email delivery without any external communication.
     *
     * @param emailMessage the email message to "send"
     * @throws IllegalArgumentException if the email message is null
     */
    @Override
    public void sendEmail(EmailMessage emailMessage) {
        if(emailMessage == null){
            throw new IllegalArgumentException("Email message cannot be null");
        }
        sentMessages.add(emailMessage);
        log.info("[TEST EMAIL GATEWAY] Email would be sent: {}", emailMessage);
    }

}

