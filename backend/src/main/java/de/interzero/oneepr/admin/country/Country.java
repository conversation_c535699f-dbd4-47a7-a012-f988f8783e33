package de.interzero.oneepr.admin.country;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.entity.CountryFollower;
import de.interzero.oneepr.admin.service_setup.ServiceSetup;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "country",
        schema = "public"
)
public class Country {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            unique = true,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "code",
            nullable = false,
            unique = true,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("code")
    private String code;

    @NotNull
    @Column(
            name = "flag_url",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("flag_url")
    private String flagUrl;

    @NotNull
    @Column(
            name = "is_published",
            nullable = false
    )
    @JsonProperty("is_published")
    private Boolean isPublished = false;

    @Column(
            name = "license_required",
            nullable = false
    )
    @JsonProperty("license_required")
    private Boolean licenseRequired;

    @NotNull
    @Column(
            name = "authorize_representative_obligated",
            nullable = false
    )
    @JsonProperty("authorize_representative_obligated")
    private Boolean authorizeRepresentativeObligated = false;

    @NotNull
    @Column(
            name = "other_costs_obligated",
            nullable = false
    )
    @JsonProperty("other_costs_obligated")
    private Boolean otherCostsObligated = false;

    @Embedded
    @JsonProperty("service_setup")
    private ServiceSetup serviceSetup = new ServiceSetup();


    @OneToMany(mappedBy = "country")
    @JsonIgnore
    @JsonProperty("followers")
    private List<CountryFollower> followers = new ArrayList<>();

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @Column(name = "updated_at")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    @PostLoad
    protected void onLoad() {
        if (this.serviceSetup == null) {
            this.serviceSetup = new ServiceSetup();
        }
    }
}