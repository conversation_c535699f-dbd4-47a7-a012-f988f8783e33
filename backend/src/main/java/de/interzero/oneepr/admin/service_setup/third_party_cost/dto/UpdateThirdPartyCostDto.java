package de.interzero.oneepr.admin.service_setup.third_party_cost.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for updating an existing third party cost.
 * All fields are optional for partial updates.
 */
@Getter
@Setter
public class UpdateThirdPartyCostDto {

    @JsonProperty("name")
    @Schema(
            description = "Name of the third party cost",
            example = "Updated Registration Fee"
    )
    private String name;

    @JsonProperty("price")
    @Schema(
            description = "Price of the third party cost in cents",
            example = "6000"
    )
    @Positive(message = "Price must be positive")
    private Integer price;
}
