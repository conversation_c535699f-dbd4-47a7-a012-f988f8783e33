package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.ReportSetFrequency;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * Data Transfer Object representing a Report Set Frequency, specifically tailored for API responses.
 * <p>
 * This DTO originates from the {@link ReportSetFrequency}
 * entity but is required to provide an enriched and client-optimized data structure. It is used
 * in responses where detailed, context-aware information about the frequency is needed.
 * <p>
 * The DTO is necessary for several reasons:
 * <ul>
 *   <li><b>JSON Parsing:</b> It transforms the raw JSON string stored in the entity's {@code frequency}
 *   field into a structured {@code Object} (e.g., a Map or List). This provides the client with
 *   pre-parsed, ready-to-use data.</li>
 *   <li><b>Nested DTO for Shaping:</b> It includes a nested {@link PackagingServiceWithCriteriaDto}
 *   instead of the raw entity, providing a tailored, limited view of the parent service, which
 *   is essential for controlling the response shape.</li>
 *   <li><b>Computed Logic:</b> It adds the computed boolean flag {@code has_criteria}, which is
 *   calculated by the service to inform the client whether a specific decision criterion is
 *   associated with this frequency, simplifying front-end conditional logic.</li>
 * </ul>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReportSetFrequencyResponseDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("rhythm")
    private ReportSetFrequency.Rhythm rhythm;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @JsonProperty("frequency")
    private Object frequency;

    @JsonProperty("packaging_service")
    private PackagingServiceWithCriteriaDto packagingService;

    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @JsonProperty("has_criteria")
    private boolean hasCriteria;
}