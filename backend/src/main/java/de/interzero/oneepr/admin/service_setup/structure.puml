@startuml
' Diagram title and styling
title Service Setup Module - Detailed Architecture Diagram
skinparam packageStyle rectangle

' Define stereotypes for styling
skinparam class {
    BackgroundColor<<Controller>> LightBlue
    BackgroundColor<<Service>> LightGreen
    BackgroundColor<<Repository>> LightGoldenRodYellow
    BackgroundColor<<Entity>> Wheat
}

' --- Main Package ---
package "service_setup" {

    ' Core components at the root level
    package "core" {
        class ServiceSetupController <<Controller>>
        class ServiceSetupService <<Service>>
        class ServiceSetupMapper
        class ReportSetMapper
        package "dto" {}
    }

    ' --- Feature: Fraction Set ---
    package "fraction_set" {
        package "report_set" {
            class ReportSetController <<Controller>>
            class ReportSetService <<Service>>
            class ReportSetRepository <<Repository>>
            class ReportSet <<Entity>>
            package "dto" {}
        }
        package "report_set_columns" {
            class ReportSetColumnsController <<Controller>>
            class ReportSetColumnsService <<Service>>
            class ReportSetColumnRepository <<Repository>>
            class ReportSetColumn <<Entity>>
            package "dto" {}
        }
        package "report_set_fractions" {
            class ReportSetFractionController <<Controller>>
            class ReportSetFractionService <<Service>>
            class ReportSetFractionRepository <<Repository>>
            class ReportSetFraction <<Entity>>
            package "dto" {}
        }
        package "report_set_price_list" {
            class ReportSetPriceListController <<Controller>>
            class ReportSetPriceListService <<Service>>
            class ReportSetPriceListRepository <<Repository>>
            class ReportSetPriceList <<Entity>>
            package "dto" {}
        }
        package "report_set_column_fractions" {
            class ReportSetColumnFractionsController <<Controller>>
            class ReportSetColumnFractionsService <<Service>>
            class ReportSetColumnFractionRepository <<Repository>>
            class ReportSetColumnFraction <<Entity>>
            package "dto" {}
        }
    }

    ' --- Feature: Obligation Check ---
    package "obligation_check" {
        package "criteria" {
            class CriteriaController <<Controller>>
            class CriteriaService <<Service>>
            class CriteriaRepository <<Repository>>
            class CriteriaOptionRepository <<Repository>>
            class Criteria <<Entity>>
            class CriteriaOption <<Entity>>
            package "dto" {}
            Criteria "1" *-- "0..*" CriteriaOption
        }
        package "obligation_check_section" {
            class ObligationCheckSectionController <<Controller>>
            class ObligationCheckSectionService <<Service>>
            class ObligationCheckSectionRepository <<Repository>>
            class ObligationCheckSection <<Entity>>
            package "dto" {}
        }
    }

    ' --- CORRECTED Feature: Service Detail ---
    package "service_detail" {
        package "packaging_service" {
            class PackagingServiceController <<Controller>>
            class PackagingServiceService <<Service>>
            class PackagingServiceRepository <<Repository>>
            class PackagingService <<Entity>>
            package "dto" {}
        }
    }

    ' --- Detailed Feature: Report Set Frequency ---
    package "report_set_frequency" {
        class ReportSetFrequencyController <<Controller>>
        class ReportSetFrequencyService <<Service>>
        class ReportSetFrequencyRepository <<Repository>>
        class ReportSetFrequency <<Entity>>
        package "dto" {}
    }

    ' --- Detailed Feature: Required Information ---
    package "required_information" {
        class RequiredInformationController <<Controller>>
        class RequiredInformationService <<Service>>
        class RequiredInformationRepository <<Repository>>
        class RequiredInformation <<Entity>>
        package "dto" {}
    }

    ' --- Feature: Third Party Cost ---
    package "third_party_cost" {
        class ThirdPartyCostController <<Controller>>
        class ThirdPartyCostService <<Service>>
        class ThirdPartyCostRepository <<Repository>>
        class ThirdPartyCost <<Entity>>
        package "dto" {}
    }
}

' --- Define High-Level Dependencies ---
core --> fraction_set
core --> obligation_check
core --> service_detail
core --> report_set_frequency
core --> required_information
core --> third_party_cost

' --- Define Conceptual Dependencies within fraction_set ---
fraction_set.report_set --> fraction_set.report_set_columns
fraction_set.report_set --> fraction_set.report_set_fractions
fraction_set.report_set --> fraction_set.report_set_price_list

@enduml