package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Data Transfer Object representing a packaging service within the context of a commitment submission result.
 * <p>
 * This DTO is a specialized view of the {@link PackagingService} entity, used as a nested
 * object within the {@code CommitmentSubmitResultDto}. It is required because the API
 * .
 * response must include calculated, context-specific fields that are not part of the
 * original entity. Specifically, it adds:
 * <ul>
 *   <li>{@code obliged}: A boolean flag determined by the service logic based on user answers.</li>
 *   <li>{@code report_set}: The single, specific {@link ReportSet} selected by the commitment logic.</li>
 *   <li>{@code report_set_frequency}: The single, specific frequency selected by the commitment logic.</li>
 * </ul>
 * Using this DTO allows the service to construct a clear and tailored response structure
 * without modifying the state of the managed {@code PackagingService} entity.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommitmentPackagingServiceDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt;


    @JsonProperty("obliged")
    private boolean obliged;

    @JsonProperty("report_set")
    private ReportSet reportSet;

    @JsonProperty("report_set_frequency")
    private ReportSetFrequencyResponseDto reportSetFrequency;

    public static CommitmentPackagingServiceDto fromEntity(PackagingService packagingService) {
        return new CommitmentPackagingServiceDto(
                packagingService.getId(),
                packagingService.getName(),
                packagingService.getDescription(),
                packagingService.getCountryId(),
                packagingService.getCreatedAt(),
                packagingService.getUpdatedAt(),
                packagingService.getDeletedAt(),
                false,
                // Default value
                null,
                null);
    }
}