package de.interzero.oneepr.admin.service_setup.third_party_cost.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new "other cost".
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateOtherCostDto extends BaseDto {

    @JsonProperty("name")
    @Schema(
            description = "Name of the other cost",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    @JsonProperty("price")
    @Schema(
            description = "Price of the other cost",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer price;

    @JsonProperty("country_id")
    @Schema(
            description = "ID of the country",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer countryId;
}
