package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * This DTO represents the detailed, serializable view of a ReportSetColumn entity.
 * It is designed to be returned from API endpoints, supporting a nested structure for children
 * and containing its associated fractions. It is a sub-component of ReportSetDetailDto.
 */
@Data
public class ReportSetColumnDetailDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("unit_type")
    private ReportSetColumn.UnitType unitType;

    @JsonProperty("parent_id")
    private Integer parentId;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @JsonProperty("level")
    private Integer level;

    @JsonProperty("order")
    private Integer order;

    @JsonProperty("code")
    private String code;

    @JsonProperty("report_set_id")
    private Integer reportSetId;

    @JsonProperty("parent_code")
    private String parentCode;

    @JsonProperty("children")
    private List<ReportSetColumnDetailDto> children;

    @JsonProperty("fractions")
    private List<ReportSetColumnFractionDetailDto> fractions;
}