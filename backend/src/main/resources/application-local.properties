#database
spring.jpa.hibernate.ddl-auto=validate
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=*****************************************
spring.datasource.username=epr-local-user
spring.datasource.password=epr-local-password
# Flyway configuration
spring.flyway.enabled=true
spring.flyway.out-of-order=true
spring.flyway.locations=classpath:db/migration
# Email configuration
spring.mail.host=localhost
spring.mail.port=1025
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=true
# Enable sandbox gateway
mail.gateway=sandbox

services.crm.base-url=https://