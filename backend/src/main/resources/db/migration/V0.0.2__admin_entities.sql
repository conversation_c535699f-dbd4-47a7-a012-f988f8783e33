--
-- Name: BrokerCompanyOrderStatus; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."BrokerCompanyOrderStatus" AS ENUM (
    'OPEN',
    'CANCELLED'
    );



--
-- Name: BrokerCompanyOrderType; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."BrokerCompanyOrderType" AS ENUM (
    'INITIAL_REPORT',
    'REPORT'
    );



--
-- Name: PriceListConditionType; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."PriceListConditionType" AS ENUM (
    'LICENSE_YEAR'
    );



--
-- Name: ReportSetColumnUnitType; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."ReportSetColumnUnitType" AS ENUM (
    'KG',
    'UNITS',
    'EACH'
    );



--
-- Name: Services; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."Services" AS ENUM (
    'CUSTOMER',
    'AUTH',
    'CALCULATOR',
    'CLERK',
    'PAYMENT',
    'SHOP',
    'SUBSCRIPTION',
    'FRONT_END'
    );



--
-- Name: TypeNotifications; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."TypeNotifications" AS ENUM (
    'EMAIL',
    'SMS'
    );



--
-- Name: criteria_calculator_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.criteria_calculator_type AS ENUM (
    'LICENSE_FEES',
    'TOTAL_IN_TONS',
    'TOTAL_IN_KG'
    );



--
-- Name: criteria_input_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.criteria_input_type AS ENUM (
    'RADIO',
    'SELECT',
    'YES_NO',
    'RANGE'
    );



--
-- Name: criteria_mode; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.criteria_mode AS ENUM (
    'COMMITMENT',
    'CALCULATOR'
    );



--
-- Name: criteria_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.criteria_type AS ENUM (
    'PACKAGING_SERVICE',
    'REPORT_SET',
    'REPORT_FREQUENCY',
    'AUTHORIZE_REPRESENTATIVE',
    'REPRESENTATIVE_TIER',
    'OTHER_COST',
    'PRICE_LIST',
    'REQUIRED_INFORMATION'
    );



--
-- Name: price_list_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.price_list_type AS ENUM (
    'EU_LICENSE',
    'DIRECT_LICENSE',
    'ACTION_GUIDE'
    );



--
-- Name: report_set_mode; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.report_set_mode AS ENUM (
    'ON_PLATAFORM',
    'BY_EXCEL',
    'FLAT_RATES'
    );



--
-- Name: report_set_price_list_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.report_set_price_list_type AS ENUM (
    'FIXED_PRICE',
    'PRICE_PER_CATEGORY',
    'PRICE_PER_VOLUME_BASE_PRICE',
    'PRICE_PER_VOLUME_MINIMUM_FEE'
    );



--
-- Name: report_set_rhythm; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.report_set_rhythm AS ENUM (
    'ANNUALLY',
    'MONTHLY',
    'QUARTERLY'
    );



--
-- Name: report_set_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.report_set_type AS ENUM (
    'FRACTIONS',
    'CATEGORIES'
    );



--
-- Name: required_information_kind; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.required_information_kind AS ENUM (
    'COUNTRY_INFORMATION',
    'GENERAL_INFORMATION'
    );



--
-- Name: required_information_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.required_information_type AS ENUM (
    'TEXT',
    'NUMBER',
    'DOCUMENT',
    'FILE',
    'IMAGE'
    );



SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public._prisma_migrations
(
    id                  character varying(36)                  NOT NULL,
    checksum            character varying(64)                  NOT NULL,
    finished_at         timestamp with time zone,
    migration_name      character varying(255)                 NOT NULL,
    logs                text,
    rolled_back_at      timestamp with time zone,
    started_at          timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer                  DEFAULT 0     NOT NULL
);



--
-- Name: broker; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.broker
(
    id           integer                                                  NOT NULL,
    name         text                                                     NOT NULL,
    email        text                                                     NOT NULL,
    phone        text                                                     NOT NULL,
    enroled_at   timestamp(3) without time zone                           NOT NULL,
    company_name text                                                     NOT NULL,
    vat          text,
    tax          text,
    created_at   timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at   timestamp(3) without time zone,
    deleted_at   timestamp(3) without time zone,
    is_active    boolean                        DEFAULT true              NOT NULL,
    user_id      integer                                                  NOT NULL
);



--
-- Name: broker_company; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.broker_company
(
    id              integer                                                  NOT NULL,
    broker_id       integer                                                  NOT NULL,
    name            text                                                     NOT NULL,
    register_number text                                                     NOT NULL,
    vat             text,
    tax             text,
    country_code    text,
    address_number  text,
    address_street  text                                                     NOT NULL,
    city            text                                                     NOT NULL,
    contact_name    text                                                     NOT NULL,
    contact_email   text                                                     NOT NULL,
    phone_number    text                                                     NOT NULL,
    file_id         integer,
    created_at      timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at      timestamp(3) without time zone,
    deleted_at      timestamp(3) without time zone
);



--
-- Name: broker_company_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.broker_company_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: broker_company_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.broker_company_id_seq OWNED BY public.broker_company.id;


--
-- Name: broker_company_order; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.broker_company_order
(
    id              integer                                                  NOT NULL,
    customer_number text                                                     NOT NULL,
    company_id      integer                                                  NOT NULL,
    transfer_date   timestamp(3) without time zone                           NOT NULL,
    order_number    text                                                     NOT NULL,
    year            integer                                                  NOT NULL,
    fractions       jsonb[],
    status          public."BrokerCompanyOrderStatus"                        NOT NULL,
    created_at      timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at      timestamp(3) without time zone,
    updated_at      timestamp(3) without time zone,
    type            public."BrokerCompanyOrderType"                          NOT NULL,
    net_value       integer                                                  NOT NULL,
    file_id         integer                                                  NOT NULL
);



--
-- Name: broker_company_order_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.broker_company_order_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: broker_company_order_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.broker_company_order_id_seq OWNED BY public.broker_company_order.id;


--
-- Name: broker_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.broker_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: broker_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.broker_id_seq OWNED BY public.broker.id;


--
-- Name: country; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.country
(
    id                                 integer                                                  NOT NULL,
    name                               text                                                     NOT NULL,
    created_at                         timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at                         timestamp(3) without time zone,
    authorize_representative_obligated boolean                        DEFAULT false             NOT NULL,
    code                               text                                                     NOT NULL,
    flag_url                           text                                                     NOT NULL,
    other_costs_obligated              boolean                        DEFAULT false             NOT NULL,
    is_published                       boolean                        DEFAULT false             NOT NULL
);



--
-- Name: country_follower; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.country_follower
(
    id              integer                                                  NOT NULL,
    country_id      integer                                                  NOT NULL,
    user_id         integer                                                  NOT NULL,
    user_email      text                                                     NOT NULL,
    user_first_name text                                                     NOT NULL,
    user_last_name  text                                                     NOT NULL,
    created_at      timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at      timestamp(3) without time zone                           NOT NULL,
    deleted_at      timestamp(3) without time zone
);



--
-- Name: country_follower_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.country_follower_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: country_follower_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.country_follower_id_seq OWNED BY public.country_follower.id;


--
-- Name: country_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.country_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: country_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.country_id_seq OWNED BY public.country.id;


--
-- Name: country_price_list; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.country_price_list
(
    id            integer                                                  NOT NULL,
    country_id    integer                                                  NOT NULL,
    price_list_id integer                                                  NOT NULL,
    created_at    timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at    timestamp(3) without time zone                           NOT NULL,
    deleted_at    timestamp(3) without time zone
);



--
-- Name: country_price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.country_price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: country_price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.country_price_list_id_seq OWNED BY public.country_price_list.id;


--
-- Name: criteria; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.criteria
(
    id                      integer                                                  NOT NULL,
    created_at              timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at              timestamp(3) without time zone,
    help_text               text,
    input_type              public.criteria_input_type,
    mode                    public.criteria_mode                                     NOT NULL,
    title                   text,
    type                    public.criteria_type                                     NOT NULL,
    updated_at              timestamp(3) without time zone                           NOT NULL,
    country_id              integer                                                  NOT NULL,
    packaging_service_id    integer,
    required_information_id integer,
    calculator_type         public.criteria_calculator_type
);



--
-- Name: criteria_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.criteria_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: criteria_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.criteria_id_seq OWNED BY public.criteria.id;


--
-- Name: criteria_option; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.criteria_option
(
    id              integer                                                  NOT NULL,
    criteria_id     integer                                                  NOT NULL,
    value           text                                                     NOT NULL,
    created_at      timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at      timestamp(3) without time zone                           NOT NULL,
    deleted_at      timestamp(3) without time zone,
    option_value    text                                                     NOT NULL,
    option_to_value text
);



--
-- Name: criteria_option_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.criteria_option_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: criteria_option_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.criteria_option_id_seq OWNED BY public.criteria_option.id;


--
-- Name: files; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.files
(
    id            text                                                     NOT NULL,
    name          text                                                     NOT NULL,
    extension     text                                                     NOT NULL,
    size          text                                                     NOT NULL,
    creator_type  text                                                     NOT NULL,
    document_type text                                                     NOT NULL,
    user_id       text,
    created_at    timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at    timestamp(3) without time zone,
    original_name text                                                     NOT NULL,
    country_id    integer
);



--
-- Name: fraction_icon; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.fraction_icon
(
    id         integer                                                  NOT NULL,
    file_id    text                                                     NOT NULL,
    image_url  text                           DEFAULT ''::text          NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone                           NOT NULL,
    deleted_at timestamp(3) without time zone
);



--
-- Name: fraction_icon_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.fraction_icon_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: fraction_icon_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.fraction_icon_id_seq OWNED BY public.fraction_icon.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.notifications
(
    id         text                                                                       NOT NULL,
    user_id    integer                                                                    NOT NULL,
    service    public."Services"              DEFAULT 'CUSTOMER'::public."Services"       NOT NULL,
    subject    text                                                                       NOT NULL,
    message    text                                                                       NOT NULL,
    type       public."TypeNotifications"     DEFAULT 'EMAIL'::public."TypeNotifications" NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP                   NOT NULL,
    updated_at timestamp(3) without time zone,
    deleted_at timestamp(3) without time zone
);



--
-- Name: other_cost; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.other_cost
(
    id         integer                                                  NOT NULL,
    name       text                                                     NOT NULL,
    price      integer                                                  NOT NULL,
    country_id integer                                                  NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone                           NOT NULL,
    deleted_at timestamp(3) without time zone
);



--
-- Name: other_cost_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.other_cost_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: other_cost_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.other_cost_id_seq OWNED BY public.other_cost.id;


--
-- Name: packaging_service; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.packaging_service
(
    id          integer                                                  NOT NULL,
    name        text                                                     NOT NULL,
    description text                                                     NOT NULL,
    country_id  integer                                                  NOT NULL,
    created_at  timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at  timestamp(3) without time zone                           NOT NULL,
    deleted_at  timestamp(3) without time zone
);



--
-- Name: packaging_service_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.packaging_service_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: packaging_service_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.packaging_service_id_seq OWNED BY public.packaging_service.id;


--
-- Name: price_list; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.price_list
(
    id                    integer                                                  NOT NULL,
    type                  public.price_list_type                                   NOT NULL,
    name                  text                                                     NOT NULL,
    description           text                                                     NOT NULL,
    start_date            timestamp(3) without time zone                           NOT NULL,
    end_date              timestamp(3) without time zone                           NOT NULL,
    basic_price           integer,
    minimum_price         integer,
    registration_fee      integer,
    variable_handling_fee double precision,
    price                 integer,
    created_at            timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at            timestamp(3) without time zone                           NOT NULL,
    deleted_at            timestamp(3) without time zone,
    condition_type        public."PriceListConditionType"                          NOT NULL,
    condition_type_value  text                                                     NOT NULL,
    handling_fee          integer,
    thresholds            jsonb
);



--
-- Name: price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.price_list_id_seq OWNED BY public.price_list.id;


--
-- Name: report_set; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.report_set
(
    id                     integer                                                  NOT NULL,
    name                   text                                                     NOT NULL,
    mode                   public.report_set_mode                                   NOT NULL,
    type                   public.report_set_type                                   NOT NULL,
    created_at             timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at             timestamp(3) without time zone                           NOT NULL,
    deleted_at             timestamp(3) without time zone,
    packaging_service_id   integer                                                  NOT NULL,
    sheet_file_description text,
    sheet_file_id          text
);



--
-- Name: report_set_column; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.report_set_column
(
    id            integer                                                  NOT NULL,
    name          text                                                     NOT NULL,
    description   text                                                     NOT NULL,
    unit_type     public."ReportSetColumnUnitType"                         NOT NULL,
    report_set_id integer                                                  NOT NULL,
    parent_id     integer,
    created_at    timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at    timestamp(3) without time zone                           NOT NULL,
    deleted_at    timestamp(3) without time zone,
    code          text                                                     NOT NULL,
    level         integer                        DEFAULT 1                 NOT NULL,
    "order"       integer                        DEFAULT 1                 NOT NULL,
    parent_code   text
);



--
-- Name: report_set_column_fraction; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.report_set_column_fraction
(
    id            integer                                                  NOT NULL,
    created_at    timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at    timestamp(3) without time zone                           NOT NULL,
    deleted_at    timestamp(3) without time zone,
    column_code   text                                                     NOT NULL,
    fraction_code text                                                     NOT NULL
);



--
-- Name: report_set_column_fraction_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.report_set_column_fraction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: report_set_column_fraction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.report_set_column_fraction_id_seq OWNED BY public.report_set_column_fraction.id;


--
-- Name: report_set_column_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.report_set_column_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: report_set_column_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.report_set_column_id_seq OWNED BY public.report_set_column.id;


--
-- Name: report_set_fraction; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.report_set_fraction
(
    id               integer                                                  NOT NULL,
    name             text                                                     NOT NULL,
    description      text                                                     NOT NULL,
    report_set_id    integer                                                  NOT NULL,
    parent_id        integer,
    created_at       timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at       timestamp(3) without time zone                           NOT NULL,
    deleted_at       timestamp(3) without time zone,
    icon             text                           DEFAULT 'aluminium'::text NOT NULL,
    is_active        boolean                        DEFAULT true              NOT NULL,
    code             text                                                     NOT NULL,
    fraction_icon_id integer,
    level            integer                        DEFAULT 1                 NOT NULL,
    "order"          integer                        DEFAULT 1                 NOT NULL,
    parent_code      text,
    has_second_level boolean                        DEFAULT false             NOT NULL,
    has_third_level  boolean                        DEFAULT false             NOT NULL
);



--
-- Name: report_set_fraction_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.report_set_fraction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: report_set_fraction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.report_set_fraction_id_seq OWNED BY public.report_set_fraction.id;


--
-- Name: report_set_frequency; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.report_set_frequency
(
    id                   integer                                                  NOT NULL,
    rhythm               public.report_set_rhythm                                 NOT NULL,
    created_at           timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at           timestamp(3) without time zone                           NOT NULL,
    deleted_at           timestamp(3) without time zone,
    frequency            jsonb                                                    NOT NULL,
    packaging_service_id integer                                                  NOT NULL
);



--
-- Name: report_set_frequency_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.report_set_frequency_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: report_set_frequency_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.report_set_frequency_id_seq OWNED BY public.report_set_frequency.id;


--
-- Name: report_set_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.report_set_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: report_set_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.report_set_id_seq OWNED BY public.report_set.id;


--
-- Name: report_set_price_list; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.report_set_price_list
(
    id            integer                                                  NOT NULL,
    report_set_id integer                                                  NOT NULL,
    title         text                                                     NOT NULL,
    license_year  integer                        DEFAULT 2025              NOT NULL,
    start_date    timestamp(3) without time zone                           NOT NULL,
    end_date      timestamp(3) without time zone                           NOT NULL,
    type          public.report_set_price_list_type                        NOT NULL,
    fixed_price   integer,
    base_price    integer,
    minimum_fee   integer,
    created_at    timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at    timestamp(3) without time zone                           NOT NULL,
    deleted_at    timestamp(3) without time zone
);



--
-- Name: report_set_price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.report_set_price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: report_set_price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.report_set_price_list_id_seq OWNED BY public.report_set_price_list.id;


--
-- Name: report_set_price_list_item; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.report_set_price_list_item
(
    id            integer                                                  NOT NULL,
    price_list_id integer                                                  NOT NULL,
    fraction_code text                                                     NOT NULL,
    price         integer                                                  NOT NULL,
    created_at    timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at    timestamp(3) without time zone                           NOT NULL,
    deleted_at    timestamp(3) without time zone
);



--
-- Name: report_set_price_list_item_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.report_set_price_list_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: report_set_price_list_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.report_set_price_list_item_id_seq OWNED BY public.report_set_price_list_item.id;


--
-- Name: representative_tier; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.representative_tier
(
    id         integer                                                  NOT NULL,
    name       text                                                     NOT NULL,
    price      integer                                                  NOT NULL,
    country_id integer                                                  NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone                           NOT NULL,
    deleted_at timestamp(3) without time zone
);



--
-- Name: representative_tier_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.representative_tier_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: representative_tier_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.representative_tier_id_seq OWNED BY public.representative_tier.id;


--
-- Name: required_information; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.required_information
(
    id          integer                                                                                          NOT NULL,
    country_id  integer,
    type        public.required_information_type                                                                 NOT NULL,
    name        text                                                                                             NOT NULL,
    description text                                                                                             NOT NULL,
    created_at  timestamp(3) without time zone   DEFAULT CURRENT_TIMESTAMP                                       NOT NULL,
    updated_at  timestamp(3) without time zone                                                                   NOT NULL,
    deleted_at  timestamp(3) without time zone,
    question    text,
    file_id     text,
    kind        public.required_information_kind DEFAULT 'COUNTRY_INFORMATION'::public.required_information_kind NOT NULL
);



--
-- Name: required_information_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.required_information_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: required_information_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.required_information_id_seq OWNED BY public.required_information.id;


--
-- Name: settings; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.settings
(
    id                        integer                                                  NOT NULL,
    key                       text                                                     NOT NULL,
    value                     jsonb                                                    NOT NULL,
    term_or_condition_file_id text,
    created_at                timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at                timestamp(3) without time zone                           NOT NULL,
    deleted_at                timestamp(3) without time zone
);



--
-- Name: settings_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.settings_id_seq OWNED BY public.settings.id;


--
-- Name: upload_file_history; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.upload_file_history
(
    id         integer                                                  NOT NULL,
    file_name  text                                                     NOT NULL,
    file_url   text                                                     NOT NULL,
    broker_id  integer                                                  NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp(3) without time zone,
    updated_at timestamp(3) without time zone
);



--
-- Name: upload_file_history_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.upload_file_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: upload_file_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.upload_file_history_id_seq OWNED BY public.upload_file_history.id;


--
-- Name: broker id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker
    ALTER COLUMN id SET DEFAULT nextval('public.broker_id_seq'::regclass);


--
-- Name: broker_company id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker_company
    ALTER COLUMN id SET DEFAULT nextval('public.broker_company_id_seq'::regclass);


--
-- Name: broker_company_order id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker_company_order
    ALTER COLUMN id SET DEFAULT nextval('public.broker_company_order_id_seq'::regclass);


--
-- Name: country id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country
    ALTER COLUMN id SET DEFAULT nextval('public.country_id_seq'::regclass);


--
-- Name: country_follower id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country_follower
    ALTER COLUMN id SET DEFAULT nextval('public.country_follower_id_seq'::regclass);


--
-- Name: country_price_list id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country_price_list
    ALTER COLUMN id SET DEFAULT nextval('public.country_price_list_id_seq'::regclass);


--
-- Name: criteria id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.criteria
    ALTER COLUMN id SET DEFAULT nextval('public.criteria_id_seq'::regclass);


--
-- Name: criteria_option id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.criteria_option
    ALTER COLUMN id SET DEFAULT nextval('public.criteria_option_id_seq'::regclass);


--
-- Name: fraction_icon id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.fraction_icon
    ALTER COLUMN id SET DEFAULT nextval('public.fraction_icon_id_seq'::regclass);


--
-- Name: other_cost id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.other_cost
    ALTER COLUMN id SET DEFAULT nextval('public.other_cost_id_seq'::regclass);


--
-- Name: packaging_service id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.packaging_service
    ALTER COLUMN id SET DEFAULT nextval('public.packaging_service_id_seq'::regclass);


--
-- Name: price_list id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.price_list
    ALTER COLUMN id SET DEFAULT nextval('public.price_list_id_seq'::regclass);


--
-- Name: report_set id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set
    ALTER COLUMN id SET DEFAULT nextval('public.report_set_id_seq'::regclass);


--
-- Name: report_set_column id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_column
    ALTER COLUMN id SET DEFAULT nextval('public.report_set_column_id_seq'::regclass);


--
-- Name: report_set_column_fraction id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_column_fraction
    ALTER COLUMN id SET DEFAULT nextval('public.report_set_column_fraction_id_seq'::regclass);


--
-- Name: report_set_fraction id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ALTER COLUMN id SET DEFAULT nextval('public.report_set_fraction_id_seq'::regclass);


--
-- Name: report_set_frequency id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_frequency
    ALTER COLUMN id SET DEFAULT nextval('public.report_set_frequency_id_seq'::regclass);


--
-- Name: report_set_price_list id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_price_list
    ALTER COLUMN id SET DEFAULT nextval('public.report_set_price_list_id_seq'::regclass);


--
-- Name: report_set_price_list_item id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_price_list_item
    ALTER COLUMN id SET DEFAULT nextval('public.report_set_price_list_item_id_seq'::regclass);


--
-- Name: representative_tier id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.representative_tier
    ALTER COLUMN id SET DEFAULT nextval('public.representative_tier_id_seq'::regclass);


--
-- Name: required_information id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.required_information
    ALTER COLUMN id SET DEFAULT nextval('public.required_information_id_seq'::regclass);


--
-- Name: settings id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.settings
    ALTER COLUMN id SET DEFAULT nextval('public.settings_id_seq'::regclass);


--
-- Name: upload_file_history id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.upload_file_history
    ALTER COLUMN id SET DEFAULT nextval('public.upload_file_history_id_seq'::regclass);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: broker_company_order broker_company_order_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker_company_order
    ADD CONSTRAINT broker_company_order_pkey PRIMARY KEY (id);


--
-- Name: broker_company broker_company_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker_company
    ADD CONSTRAINT broker_company_pkey PRIMARY KEY (id);


--
-- Name: broker broker_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker
    ADD CONSTRAINT broker_pkey PRIMARY KEY (id);


--
-- Name: country_follower country_follower_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country_follower
    ADD CONSTRAINT country_follower_pkey PRIMARY KEY (id);


--
-- Name: country country_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country
    ADD CONSTRAINT country_pkey PRIMARY KEY (id);


--
-- Name: country_price_list country_price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country_price_list
    ADD CONSTRAINT country_price_list_pkey PRIMARY KEY (id);


--
-- Name: criteria_option criteria_option_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.criteria_option
    ADD CONSTRAINT criteria_option_pkey PRIMARY KEY (id);


--
-- Name: criteria criteria_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT criteria_pkey PRIMARY KEY (id);


--
-- Name: files files_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_pkey PRIMARY KEY (id);


--
-- Name: fraction_icon fraction_icon_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.fraction_icon
    ADD CONSTRAINT fraction_icon_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: other_cost other_cost_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.other_cost
    ADD CONSTRAINT other_cost_pkey PRIMARY KEY (id);


--
-- Name: packaging_service packaging_service_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.packaging_service
    ADD CONSTRAINT packaging_service_pkey PRIMARY KEY (id);


--
-- Name: price_list price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.price_list
    ADD CONSTRAINT price_list_pkey PRIMARY KEY (id);


--
-- Name: report_set_column_fraction report_set_column_fraction_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_column_fraction
    ADD CONSTRAINT report_set_column_fraction_pkey PRIMARY KEY (id);


--
-- Name: report_set_column report_set_column_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_column
    ADD CONSTRAINT report_set_column_pkey PRIMARY KEY (id);


--
-- Name: report_set_fraction report_set_fraction_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ADD CONSTRAINT report_set_fraction_pkey PRIMARY KEY (id);


--
-- Name: report_set_frequency report_set_frequency_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_frequency
    ADD CONSTRAINT report_set_frequency_pkey PRIMARY KEY (id);


--
-- Name: report_set report_set_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set
    ADD CONSTRAINT report_set_pkey PRIMARY KEY (id);


--
-- Name: report_set_price_list_item report_set_price_list_item_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_price_list_item
    ADD CONSTRAINT report_set_price_list_item_pkey PRIMARY KEY (id);


--
-- Name: report_set_price_list report_set_price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_price_list
    ADD CONSTRAINT report_set_price_list_pkey PRIMARY KEY (id);


--
-- Name: representative_tier representative_tier_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.representative_tier
    ADD CONSTRAINT representative_tier_pkey PRIMARY KEY (id);


--
-- Name: required_information required_information_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.required_information
    ADD CONSTRAINT required_information_pkey PRIMARY KEY (id);


--
-- Name: settings settings_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.settings
    ADD CONSTRAINT settings_pkey PRIMARY KEY (id);


--
-- Name: upload_file_history upload_file_history_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.upload_file_history
    ADD CONSTRAINT upload_file_history_pkey PRIMARY KEY (id);


--
-- Name: broker_email_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX broker_email_key ON public.broker USING btree (email);


--
-- Name: broker_user_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX broker_user_id_key ON public.broker USING btree (user_id);


--
-- Name: country_code_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX country_code_key ON public.country USING btree (code);


--
-- Name: country_name_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX country_name_key ON public.country USING btree (name);


--
-- Name: fraction_icon_file_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX fraction_icon_file_id_key ON public.fraction_icon USING btree (file_id);


--
-- Name: report_set_column_code_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX report_set_column_code_key ON public.report_set_column USING btree (code);


--
-- Name: report_set_fraction_code_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX report_set_fraction_code_key ON public.report_set_fraction USING btree (code);


--
-- Name: report_set_sheet_file_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX report_set_sheet_file_id_key ON public.report_set USING btree (sheet_file_id);


--
-- Name: required_information_file_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX required_information_file_id_key ON public.required_information USING btree (file_id);


--
-- Name: settings_key_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX settings_key_key ON public.settings USING btree (key);


--
-- Name: settings_term_or_condition_file_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX settings_term_or_condition_file_id_key ON public.settings USING btree (term_or_condition_file_id);


--
-- Name: broker_company broker_company_broker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker_company
    ADD CONSTRAINT broker_company_broker_id_fkey FOREIGN KEY (broker_id) REFERENCES public.broker (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: broker_company broker_company_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker_company
    ADD CONSTRAINT broker_company_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.upload_file_history (id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: broker_company_order broker_company_order_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker_company_order
    ADD CONSTRAINT broker_company_order_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.broker_company (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: broker_company_order broker_company_order_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.broker_company_order
    ADD CONSTRAINT broker_company_order_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.upload_file_history (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: country_follower country_follower_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country_follower
    ADD CONSTRAINT country_follower_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: country_price_list country_price_list_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country_price_list
    ADD CONSTRAINT country_price_list_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: country_price_list country_price_list_price_list_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.country_price_list
    ADD CONSTRAINT country_price_list_price_list_id_fkey FOREIGN KEY (price_list_id) REFERENCES public.price_list (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: criteria criteria_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT criteria_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: criteria_option criteria_option_criteria_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.criteria_option
    ADD CONSTRAINT criteria_option_criteria_id_fkey FOREIGN KEY (criteria_id) REFERENCES public.criteria (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: criteria criteria_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT criteria_packaging_service_id_fkey FOREIGN KEY (packaging_service_id) REFERENCES public.packaging_service (id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: criteria criteria_required_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT criteria_required_information_id_fkey FOREIGN KEY (required_information_id) REFERENCES public.required_information (id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: fraction_icon fraction_icon_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.fraction_icon
    ADD CONSTRAINT fraction_icon_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: other_cost other_cost_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.other_cost
    ADD CONSTRAINT other_cost_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: packaging_service packaging_service_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.packaging_service
    ADD CONSTRAINT packaging_service_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_column_fraction report_set_column_fraction_column_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_column_fraction
    ADD CONSTRAINT report_set_column_fraction_column_code_fkey FOREIGN KEY (column_code) REFERENCES public.report_set_column (code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_column_fraction report_set_column_fraction_fraction_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_column_fraction
    ADD CONSTRAINT report_set_column_fraction_fraction_code_fkey FOREIGN KEY (fraction_code) REFERENCES public.report_set_fraction (code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_column report_set_column_parent_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_column
    ADD CONSTRAINT report_set_column_parent_code_fkey FOREIGN KEY (parent_code) REFERENCES public.report_set_column (code) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: report_set_column report_set_column_report_set_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_column
    ADD CONSTRAINT report_set_column_report_set_id_fkey FOREIGN KEY (report_set_id) REFERENCES public.report_set (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_fraction report_set_fraction_fraction_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ADD CONSTRAINT report_set_fraction_fraction_icon_id_fkey FOREIGN KEY (fraction_icon_id) REFERENCES public.fraction_icon (id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: report_set_fraction report_set_fraction_parent_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ADD CONSTRAINT report_set_fraction_parent_code_fkey FOREIGN KEY (parent_code) REFERENCES public.report_set_fraction (code) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: report_set_fraction report_set_fraction_report_set_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ADD CONSTRAINT report_set_fraction_report_set_id_fkey FOREIGN KEY (report_set_id) REFERENCES public.report_set (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_frequency report_set_frequency_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_frequency
    ADD CONSTRAINT report_set_frequency_packaging_service_id_fkey FOREIGN KEY (packaging_service_id) REFERENCES public.packaging_service (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set report_set_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set
    ADD CONSTRAINT report_set_packaging_service_id_fkey FOREIGN KEY (packaging_service_id) REFERENCES public.packaging_service (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_price_list_item report_set_price_list_item_fraction_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_price_list_item
    ADD CONSTRAINT report_set_price_list_item_fraction_code_fkey FOREIGN KEY (fraction_code) REFERENCES public.report_set_fraction (code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_price_list_item report_set_price_list_item_price_list_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_price_list_item
    ADD CONSTRAINT report_set_price_list_item_price_list_id_fkey FOREIGN KEY (price_list_id) REFERENCES public.report_set_price_list (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_price_list report_set_price_list_report_set_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set_price_list
    ADD CONSTRAINT report_set_price_list_report_set_id_fkey FOREIGN KEY (report_set_id) REFERENCES public.report_set (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set report_set_sheet_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_set
    ADD CONSTRAINT report_set_sheet_file_id_fkey FOREIGN KEY (sheet_file_id) REFERENCES public.files (id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: representative_tier representative_tier_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.representative_tier
    ADD CONSTRAINT representative_tier_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: required_information required_information_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.required_information
    ADD CONSTRAINT required_information_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country (id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: required_information required_information_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.required_information
    ADD CONSTRAINT required_information_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files (id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: settings settings_term_or_condition_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.settings
    ADD CONSTRAINT settings_term_or_condition_file_id_fkey FOREIGN KEY (term_or_condition_file_id) REFERENCES public.files (id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: upload_file_history upload_file_history_broker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.upload_file_history
    ADD CONSTRAINT upload_file_history_broker_id_fkey FOREIGN KEY (broker_id) REFERENCES public.broker (id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- PostgreSQL database dump complete
--

