{"name": "lizenzero-admin-portal", "version": "0.1.0", "private": true, "main": "index.tsx", "packageManager": "pnpm@10.4.1+sha512.c753b6c3ad7afa13af388fa6d808035a008e30ea9993f58c6663e2bc5ff21679aa834db094987129aa4d488b86df57f7b634981b2f827cdcacc698cc0cfb88af", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint . --ext ts --ext tsx --ext js", "format": "prettier --write .", "test": "jest", "check-all": "pnpm run check-format && pnpm run check-lint && pnpm run check-types && pnpm run build", "prepare": "cd ../.. && husky install frontend/admin-portal/.husky"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@interzero/oneepr-react-ui": "1.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "accept-language-parser": "^1.5.0", "axios": "^1.6.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cypress": "^13.6.1", "date-fns": "^4.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.525.0", "mapbox-gl": "^3.7.0", "next": "14.2.30", "next-auth": "4.24.11", "notistack": "^3.0.2", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-input-mask": "^2.0.4", "react-map-gl": "^7.1.7", "react-number-format": "^5.4.4", "react-password-strength-bar": "^0.4.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.75"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@tanstack/eslint-plugin-query": "^5.66.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@types/accept-language-parser": "^1.5.8", "@types/jest": "^29.5.14", "@types/node": "^20.17.19", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-input-mask": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-promise": "^6.6.0", "eslint-plugin-react": "^7.37.4", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "engines": {"node": ">=20", "pnpm": ">=10"}}